import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity('webhook_deliveries')
export class WebhookDelivery {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string; // Customer ID

  @Column({ nullable: true })
  streamId: string; // Which stream this belongs to

  @Column()
  webhookUrl: string;

  @Column({ type: 'jsonb' })
  payload: any; // What we sent

  @Column({ default: 0 })
  retryCount: number;

  @Column()
  status: 'pending' | 'success' | 'failed' | 'abandoned';

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  reason: string; // Error message if failed

  @Column({ nullable: true })
  responseTime: number; // How long delivery took (ms)
}