import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";

@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  userId: string; // Which customer this affects (computed during routing)

  @Column()
  networkId: number; // FK to Networks table

  @Column({ unique: true })
  txHash: string;

  @Column({ nullable: true })
  blockHash: string;

  @Column()
  blockNumber: number;

  @Column()
  blockTime: Date;


  @Column()
  fromAddress: string;

  @Column()
  toAddress: string;

  @Column({ nullable: true, default: false })
  approved: boolean;

  @Column()
  value: string; // Use string for big numbers

  @Column({ nullable: true })
  gasPrice: string;

  @Column({ nullable: true })
  gasUsed: string;

  @Column()
  status: 'success' | 'failed';

  @CreateDateColumn()
  createdAt: Date;

  // Raw data from QuickNode for debugging
  @Column({ type: 'jsonb', nullable: true })
  rawData: any;

}
